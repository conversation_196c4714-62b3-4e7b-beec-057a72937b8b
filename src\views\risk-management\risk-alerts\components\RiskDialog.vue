<script lang='ts' setup>
import { watch, ref } from 'vue'
import FxgkTable from '@/views/risk-management/risk-alerts/components/FxgkTable.vue'
import Fxlxbhqst from '@/views/risk-management/risk-alerts/components/Fxlxbhqst.vue'
import Fxlxfbt from '@/views/risk-management/risk-alerts/components/Fxlxfbt.vue'
import type { IRiskListItem } from '@/types/lead'
import type { IGetRiskTypeData, IRiskEntListItem } from '@/types/indicator'
import indicatorService from '@/service/indicatorService'

const props = defineProps<{
    visible: boolean
    companyInfo: IRiskListItem | null
}>()
const dialogVisible = ref(false)
const riskTypeList = ref<IGetRiskTypeData[]>([])
const allTableData = ref<IRiskEntListItem[]>([])
const pieLoading=ref(false)
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
})
const emit = defineEmits(['update:visible'])
const handleClose = () => {
    selectYear.value='2025'
    emit('update:visible', false)
}
const getRiskTypeData = () => {
    pieLoading.value=true
    indicatorService.getRiskTypeData(
        {socialCreditCode:props.companyInfo?.socialCreditCode as string}
    ).then(res => {
        riskTypeList.value = Object.entries(res).map(([name, value]) => ({
            name,
            value
        }))
    }).finally(() => {
        pieLoading.value= false
    })
}
const getRiskEntList = async () => {
    await indicatorService
        .getRiskEntList({
            socialCreditCode: props.companyInfo?.socialCreditCode as string,
        })
        .then((res) => {
            console.log('企业风险列表', res)
            const { success, data } = res
            if (success) {
                allTableData.value = data
            }
        })
}
watch(() => props.visible, async(newVal) => {
    if (newVal) {
        getRiskTypeData() // 获取风险类型分布echart数据
        // 初始化数据
        await getRiskEntList()
    }
})
const selectYear = ref('2025')

</script>
<template>
    <el-dialog
        v-model="dialogVisible"
        :title="companyInfo?.companyName"
        width="800"
        show-close
        destroy-on-close
        style="padding: 16px 24px"
        @close="handleClose()"
    >
        <FxgkTable :allTableData="allTableData" :riskTypeList="riskTypeList"></FxgkTable>
        <div class="display-flex space-between top-bottom-center t-margin-12">
            <div class="display-flex top-bottom-center">
                <div class="w-4 h-16 border-radius-8 r-margin-6" style="background-color: var(--el-color-primary)"></div>
                <div class="font-16 color-black font-weight-500">风险类型变化趋势图</div>
            </div>
            <div>
                <el-select v-model="selectYear" class="no-border">
                    <el-option
                        v-for="item in ['2021', '2022', '2023', '2024', '2025']"
                        :key="item"
                        :value="item"
                        :label="item"
                    ></el-option>
                </el-select>
            </div>
        </div>
        <Fxlxbhqst :allTableData="allTableData" :riskTypeList="riskTypeList" :pieLoading="pieLoading" :selectYear="selectYear"/>
        <div class="display-flex b-margin-12">
            <div class="w-4 h-16 border-radius-8 r-margin-6" style="background-color: var(--el-color-primary);"></div>
            <div class="font-16 color-black font-weight-500">风险类型分布图</div>
        </div>
        <Fxlxfbt :allTableData="allTableData" :riskTypeList="riskTypeList" :pieLoading="pieLoading"></Fxlxfbt>
    </el-dialog>
</template>
<style scoped lang='scss'>
</style>
