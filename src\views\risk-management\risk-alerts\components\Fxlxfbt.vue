<script lang='ts' setup>
import { ref, watch, onMounted } from 'vue'
import PieChart from '@/components/echart/PieChart.vue'
import type { PieChartOption } from '@/types/echart'
import type { IGetRiskTypeData, IRiskEntListItem } from '@/types/indicator'
const props = defineProps<{
    allTableData: IRiskEntListItem[] | []
    riskTypeList: IGetRiskTypeData[] | []
    pieLoading: boolean
}>()
const pieOption = ref<PieChartOption>({
    // 提示框配置（鼠标悬停时显示数据详情）
    tooltip: {
        trigger: 'item', // 触发类型：数据项
        formatter: '{b}: {c}', // 自定义提示框内容格式
    },
    // 标题配置（显示在底部中间）
    title: {
        show: false, // 是否展示标题
    },
    // 图例配置（垂直显示在右侧中间）
    legend: {
        icon: 'vertical', // 核心配置：将图例标记设为圆形
        orient: 'vertical', // 排列方向：horizontal水平 vertical垂直
        // type: 'scroll',
        top: 'center', 
        right: 10, // 垂直居中
        itemGap: 10, // 图例项间距
        textStyle: {
            // 图例文字样式
            color: '#666', // 字体颜色
            fontSize: 12,
        },
    },
    // 数据系列配置（核心部分）
    series: [
        {
            // name: '数据类别', // 系列名称（显示在提示框）
            type: 'pie', // 图表类型：饼图
            center: ['25%', '50%'], // 核心修改点：调整饼图中心位置
            radius: '50%',

            // 自定义数据颜色（按顺序对应data数组）
            // color: ['#71A3E2', '#E8CC70'],
            // 数据标签配置
            label: {
                show: true, // 显示标签
                position: 'outside', // 显示的位置
                formatter: '{b}', // 显示名称+百分比
                fontSize: 14,
                color: '#333',
            },

            // 标签引导线设置
            labelLine: {
                show: true, // 显示引导线
                length: 10, // 第一段引导线长度
                length2: 15, // 第二段引导线长度
                lineStyle: {
                    width: 1,
                    type: 'solid',
                    color: '#999',
                },
            },

            // 高亮状态样式
            emphasis: {
                label: {
                    show: true,
                    fontSize: 18, // 高亮时放大标签
                    fontWeight: 'bold',
                },
                scale: true, // 高亮时略微放大区块
                scaleSize: 5, // 放大像素值
            },
            // 数据内容（核心数据）
            data: [],
        },
    ],
})
watch(() => props.riskTypeList, (newVal) => {
    pieOption.value.series[0].data = newVal
}, {
    deep: true,
})
onMounted(()=>{
    pieOption.value.series[0].data = props.riskTypeList
})
</script>
<template>
    <div v-if="!pieLoading" style="width: 100%; height: 300px;" class="display-flex center">
        <PieChart v-if="pieOption.series[0].data?.length" :pieOption="pieOption"/>
        <img v-else class="w-263" src="@/assets/images/company/qyfx-no-data.png" alt="暂无数据" />
    </div>
</template>
<style scoped lang='scss'>
</style>
