<template>
    <el-dialog
        :title="props.title"
        v-model="dialogVisible"
        width="50%"
        @close="handleClose"
    >
        <el-form 
            ref="formRef"
            class="lr-padding-8"
            :model="form"
            :inline="true"
            :rules="rules"    
        >
            <el-form-item
                label="短信渠道"
                prop="channel"
            >
                <el-select 
                    v-model="form.channel"
                    placeholder="请选择短信渠道"
                    clearable
                >
                    <el-option label="阿里云" value="ALI"></el-option>
                    <el-option label="腾讯云" value="TENCENT"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item
                label="模板id"
                prop="templateId"
            >
                <el-input v-model="form.templateId" placeholder="请输入模板id"></el-input>
            </el-form-item>
            <el-form-item
                label="模板名称"
                prop="templateName"
            >
                <el-input v-model="form.templateName" placeholder="请输入模板名称"></el-input>
            </el-form-item>
            <el-form-item
                label="key（AccessKey/AppId）"
                prop="accessKey"
            >
                <el-input v-model="form.accessKey" placeholder="请输入key"></el-input>
            </el-form-item>
            <el-form-item
                label="secretKey(AccesskeySecret/Appkey)"
                prop="secretKey"
            >
                <el-input v-model="form.secretKey" placeholder="请输入Secret"></el-input>
            </el-form-item>
            <el-form-item
                label="短信签名"
                prop="signName"
            >
                <el-input v-model="form.signName" placeholder="请输入短信签名"></el-input>
            </el-form-item>
            <el-form-item
                class="sms-content"
                label="短信内容"
                prop="content"
            >
                <el-input v-model="form.content" placeholder="请输入短信内容"></el-input>
            </el-form-item>
            <el-form-item
                class="sms-enable"
                label="是否启用"
                prop="enable"
            >
                <el-switch v-model="form.enable"></el-switch>
            </el-form-item>
            <el-form-item>
            </el-form-item>
            <el-form-item
                class="sms-button"
            >

                <el-button @click="reset(formRef)">取 消</el-button>
                <el-button :loading="buttonLoading" type="primary" @click="submit(formRef)">
                    提 交
                </el-button>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>

<script lang='ts' setup>
import { computed, defineProps, reactive, ref, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import type { ISMSTemplateAddOrEditParams, ISMSTemplatePageResponseItem } from '@/types/sms'
import systemService from '@/service/systemService'

const formRef = ref<FormInstance>()

// 初始表单数据
const initialFormData: ISMSTemplateAddOrEditParams = {
    channel:'',
    templateId:'',
    templateName:'',
    accessKey:'',
    secretKey:'',
    signName:'',
    content:'',
    enable:false,
}

const form = ref<ISMSTemplateAddOrEditParams>({...initialFormData})
const regionId = computed(() => {
    return form.value.templateId || ''
})
const addOrEditParams = ref<ISMSTemplateAddOrEditParams>()
const rules = reactive<FormRules>({
    channel:[
        {
            required: true,
            message: '请选择短信渠道',
            trigger: ['blur', 'change'],
        }
    ],
    templateId:[
        {
            required: true,
            message: '请输入模板id',
            trigger: ['blur', 'change'],
        }
    ],
    templateName:[
        {
            required: true,
            message: '请输入模板名称',
            trigger: ['blur', 'change'],
        }
    ],
    accessKey:[
        {
            required: true,
            message: '请输入key',
            trigger: ['blur', 'change'],
        }
    ],
    secretKey:[
        {
            required: true,
            message: '请输入Secret',
            trigger: ['blur', 'change'],
        }
    ],
    signName:[
        {
            required: true,
            message: '请输入短信签名',
            trigger: ['blur', 'change'],
        }
    ],
    content:[
        {
            required: true,
            message: '请输入短信内容',
            trigger: ['blur', 'change'],
        }
    ]
})
const emit = defineEmits(['update:visible'])

const clearFormData = () => {
    form.value = {...initialFormData}
    if (formRef.value) {
        formRef.value.clearValidate()
    }
}

const handleClose = () => {
    clearFormData()
    emit('update:visible', false)
}
const dialogVisible = ref<boolean>(false)

const props = defineProps<{
    visible:boolean,
    title:string
    editItem:ISMSTemplatePageResponseItem | undefined
    refresh:()=>void
}>()

watch(() => props.visible, (val) => {
    dialogVisible.value = val
})

watch(() => props.editItem, (val) => {
    console.log('editItem',val)
    if(val){
        form.value.accessKey = val.accessKey
        form.value.channel = val.channel
        form.value.content = val.content
        form.value.enable = val.enable
        form.value.templateName = val.templateName
        form.value.secretKey = val.secretKey
        form.value.signName = val.signName
        form.value.templateId = val.templateId
    }
})

const reset = (formEl: FormInstance | undefined) => {
    console.log('formEl',formEl)
    if (!formEl) return
    formEl.resetFields()
    clearFormData()
    handleClose()
}
const buttonLoading = ref<boolean>(false)
const submit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            buttonLoading.value = true
            addOrEditParams.value = form.value
            addOrEditParams.value.regionId = regionId.value
            console.log('addOrEditParams',addOrEditParams.value)
            if(props.title.includes('新增')){
                systemService.smsTemplateAdd(addOrEditParams.value).then((res) => {
                    console.log('res',res)
                    if(res.success){
                        ElMessage.success('新增成功')
                        formEl.resetFields()
                        clearFormData()
                    }else{
                        ElMessage.error(res.errMsg)
                    }
                }).finally(() => {
                    buttonLoading.value = false
                    handleClose()
                    props.refresh()
                })
            }else{
                addOrEditParams.value.id = props.editItem?.id
                systemService.smsTemplateUpdate(addOrEditParams.value).then(() => {
                    ElMessage.success('编辑成功')
                    formEl.resetFields()
                    clearFormData()
                }).finally(() => {
                    buttonLoading.value = false
                    handleClose()
                    props.refresh()
                })
            }

        } else {
            console.log('提交失败', fields)
        }
    })
}

</script>

<style lang='scss' scoped>

.el-form--inline .el-form-item{
    flex-direction: column;
    width: 45%;
    gap: 4px;
}
:deep(.el-form-item__label){
    justify-content: flex-start !important;
}
:deep(.el-form-item__content){
    justify-content: flex-end !important;
}

.sms-content{
    width: 95% !important;
}
.sms-enable{
    width: 20% !important;
    flex-direction:row !important;
}

.sms-button{
    width: 100% !important;
}
</style>