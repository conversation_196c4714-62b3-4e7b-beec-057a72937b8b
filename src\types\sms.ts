import type { IPaginationResponse } from './axios'
import type { IAllRecord } from './record'

export interface IPaginationParams extends IAllRecord {
    page:number,
    pageSize:number
}

export interface ISMSTemplatePageResponse extends IPaginationResponse {
    data:ISMSTemplatePageResponseItem[]
}

export interface ISMSTemplatePageResponseItem {
    accessKey:string,
    channel:string,
    content:string,
    createTime:number,
    id:string,
    createUser:string,
    enable:boolean,
    name:string,
    orgId:string,
    regionId:string,
    secretKey:string,
    signName:string,
    templateId:string,
    templateName:string,
    tenantId:string,
    updateTime:number
}

export interface ISMSTemplateAddOrEditParams extends IAllRecord {
    accessKey: string,
    channel: string,
    content: string,
    enable: boolean,
    id?: string,
    templateName: string,
    regionId?: string,
    secretKey: string,
    signName: string,
    templateId: string, 
}

export interface ISMSLogPageResponse extends IPaginationResponse {
    data:ISMSLogPageResponseItem[]
}

export interface ISMSLogPageResponseItem {
    createTime: number,
    createUser: string,
    id: string,
    mobile: string,
    modelId: string,
    orgId: string,
    reason: string,
    sendDate: number,
    status: number,
    taskId: string,
    tenantId: string,
    updateTime:number
}

