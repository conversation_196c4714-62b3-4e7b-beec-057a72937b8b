<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue'
import LineChart from '@/components/echart/LineChart.vue'
import type { LineChartOption } from '@/types/echart'
import type { IGetRiskTypeData, IRiskEntListItem } from '@/types/indicator'
const props = defineProps<{
    allTableData: IRiskEntListItem[] | []
    riskTypeList: IGetRiskTypeData[] | []
    pieLoading: boolean
    selectYear: string
}>()
const lineOption = ref<LineChartOption>({
    tooltip: {
        trigger: 'axis',
        formatter: function(params) {
            // params是数组，包含当前横坐标所有系列的数据
            let tipContent = ''
            params.forEach(item => {
                // 显示系列名称和数值
                tipContent += `${item.seriesName}: ${item.value}个<br/>`
            })
            // 添加横坐标信息
            return `${params[0].name}月<br/>${tipContent}`
        },
        axisPointer: {
            type: 'shadow',
        },
    },
    legend: {
        // 将图例放在正下方
        bottom: 0, // 距离底部0px
        left: 'center', // 水平居中
        orient: 'horizontal', // 水平排列
    },
    yAxis: {
        type: 'value',
        // min: 0,
        // max: 20,
        axisLine: {
            show: false,
        },
        axisTick: {
            show: false,
        },
        axisLabel: {
            show: true,
        },
    },
    xAxis: {
        type: 'category',
        data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
        axisTick: {
            show: false,
        },
    },
    series: [],
})


const dealData = (arr: IRiskEntListItem[]) => {
    const dealResultData = {}
    arr.forEach((item) => {
        const year = item.sortTime.split('-')[0] // 获取年份
        const month = parseInt(item.sortTime.split('-')[1]) - 1 // 获取月份（0-11）
        const typeName = item.typeName // 获取类型名称

        // 如果该类型不存在于结果对象中，初始化
        if (!dealResultData[year]) {
            dealResultData[year] = []
        }

        // 找到该年份的类型并更新数据
        let typeExists = dealResultData[year].some((type) => type.name === typeName)

        if (!typeExists) {
            // 初始化类型数据
            dealResultData[year].push({
                name: typeName,
                type: 'bar',
                stack: 'total',
                label: {
                    show: true,
                    formatter: function(params) {
                        // 值为0时不显示标签
                        return params.value === 0 ? '' : params.value
                    }
                },
                emphasis: {
                    focus: 'series',
                },
                data: Array(12).fill(0), // 每年12个月的数据，初始为0
            })
        }

        // 更新对应类型的月份数据
        const typeData = dealResultData[year].find((type) => type.name === typeName)
        typeData.data[month] += 1
    })
    console.log('dealResultData', dealResultData)
    lineOption.value.series = dealResultData[props.selectYear]
}
watch(
    () => props.allTableData,
    (newVal) => {

        if (newVal.length > 0) {
            dealData(newVal)
        }
    },
    {
        deep: true,
    }
)
watch(
    () => props.selectYear,
    () => {
        dealData(props.allTableData)
    }
)
onMounted(()=>{
    dealData(props.allTableData)
})
</script>
<template>
    <div style="width: 100%; height: 300px" class="display-flex center">
        <LineChart v-if="lineOption.series?.length" :lineOption="lineOption" />
        <img v-else class="w-263" src="@/assets/images/company/qyfx-no-data.png" alt="暂无数据" />
    </div>
</template>
<style scoped lang="scss">
.no-border .el-select {
    border: none !important;
    box-shadow: none !important;
}
</style>
