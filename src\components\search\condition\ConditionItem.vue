<template>
    <div class="display-flex gap-16 t-margin-10 relative condition-item tb-padding-8 top-bottom-center">

        <el-popover :visible="conditionPopover" ref="popoverRef" placement="right" trigger="click" width="700">
            <template #reference>
                <div @click="conditionPopover = true"
                     class="high-search-rules-item-key border-radius-4 pointer display-flex top-bottom-center space-between">
                    <span> {{ conditionData.propLabel ||
                        '请选择' }}</span>
                    <span class="display-flex top-bottom-center">
                        <el-icon>
                            <ArrowDown />
                        </el-icon>
                    </span>
                </div>
            </template>

            <div class="chose-condition-box" v-click-outside="onClickOutside">
                <div class="chose-condition-input b-padding-10">
                    <el-input v-model.trim="searchKey" placeholder="请输入筛选条件关键词">
                        <template #prefix>
                            <el-icon>
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </div>
                <div class="t-margin-10 display-flex ">
                    <el-scrollbar height="400px">
                        <div class="chose-condition-left r-padding-24 border-box">

                            <div class="chose-condition-category-item tb-padding-12 lr-padding-16 pointer t-margin-16 border-box"
                                 :class="activeCategoryName === category.name ? 'active-chose-condition-category-item' : ''"
                                 v-for="category in cacheHighSearchRulesData" :key="category.name"
                                 @click="activeCategoryName = category.name">
                                {{ category.name }}
                            </div>

                        </div>
                    </el-scrollbar>
                    <div class="chose-condition-right lr-padding-24 flex-1 tb-padding-16">
                        <el-scrollbar height="400px">
                            <div v-if="searchKey" class="b-margin-10">
                                <span>以下筛选条件中包含</span><span class="color-blue">【{{ searchKey }}】</span>
                            </div>
                            <div class="display-flex gap-16 flex-wrap">
                                <div v-for="condition in searchConditions" :key="condition.prop"
                                     @click="choseCondition(condition)"
                                     class="pointer children-condition-item border-radius-4 text-center lr-padding-8 tb-padding-4 display-flex border top-bottom-center left-right-center">
                                    {{ condition.name }}
                                </div>
                            </div>
                        </el-scrollbar>
                    </div>
                </div>
            </div>

        </el-popover>

        <div class="high-search-rules-item-operator" v-if="conditionData.prop && conditionData.dataType !== 'select'">
            <el-select v-model="conditionData.operator">
                <el-option label="包含任一" value="IN" />
                <el-option label="都不包含" value="NOT_IN" />
            </el-select>
        </div>
        <div class="high-search-rules-item-value" v-if="conditionData.prop">
            <el-form ref="formRef" :model="conditionData" class="demo-ruleForm">
                <el-form-item prop="value" :rules="[
                    { required: true, message: '请输入必选项' },
                ]">

                    <el-select class="width-100"
                               v-if="conditionData.dataType === 'select' || conditionData.dataType === 'multiSelect'"
                               v-model="conditionData.value" :multiple="conditionData.dataType === 'multiSelect'" clearable
                               @change="handleSelectChange">
                        <el-option v-for="item in getConditionEnums()" :key="item.tagValue" :label="item.name"
                                   :value="item.tagValue" />
                    </el-select>
                    <el-input-tag v-model="conditionData.value" v-else-if="conditionData.dataType === 'itemInput'"
                                  :placeholder="'请输入' + conditionData.propLabel" clearable />
                    <div v-else-if="conditionData.dataType === 'number'" class="display-flex">
                        <div class="flex-1"><el-input v-model.number="conditionData.value[0]"></el-input>
                        </div>
                        <div class="text-center" style="width: 20px;">-</div>
                        <div class="flex-1"><el-input v-model.number="conditionData.value[1]"></el-input></div>
                    </div>

                    <el-date-picker
                        v-else-if="conditionData.dataType === 'dateRangeMultiSelect' || conditionData.dataType === 'date'"
                        v-model="conditionData.value" type="daterange" start-placeholder="开始时间" end-placeholder="结束时间"
                        value-format="x" format="YYYY-MM-DD" date-format="YYYY/MM/DD" />

                    <el-cascader 
                        ref="cascaderRef" 
                        filterable 
                        :options="getConditionEnums()" 
                        :props="{ multiple: true, checkStrictly: true }"
                        :show-all-levels="false"
                        v-model="conditionData.value" class=" width-100"
                        v-else-if="conditionData.dataType === 'mapped' || conditionData.dataType === 'area'"
                        placeholder="" clearable 
                        @change="handleCascaderChange"    
                    />
                    <span class="l-margin-10">{{ conditionData.unit }}</span>
                </el-form-item>
            </el-form>
        </div>
        <div>
            <el-icon class="pointer" @click="removeCondition">
                <Delete />
            </el-icon>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, defineProps, computed, defineEmits, watch } from 'vue'
import type { ISearchConditions, IHighSearchRuleItem, IHighSearchRules } from '@/types/model'
import type { Ref } from 'vue'
import { useStore } from 'vuex'
import { ClickOutside as vClickOutside } from 'element-plus'

const conditionPopover: Ref<boolean> = ref(false)

const store = useStore()
const cacheHighSearchRulesData = store.state.app.hightSearchRulesData

const popoverRef = ref()

const cascaderRef = ref()


const activeCategoryName: Ref<string> = ref('')

if (cacheHighSearchRulesData) {
    activeCategoryName.value = cacheHighSearchRulesData?.[0].name || ''
}

// console.log('activeCategoryName', activeCategoryName.value)

const props = defineProps({
    conditionItem: {
        type: Object,
        default: () => {
            return {
                operator: 'IN',
                prop: '',
                propLabel: '',
                value: [],
                name: ''
            }
        },
    },
})

const emits = defineEmits(['updateCondition', 'removeCondition'])

const conditionData: Ref<ISearchConditions> = ref({
    operator: 'IN',
    prop: '',
    value: []
})

let cloneConditionItem = JSON.parse(JSON.stringify(props.conditionItem))
if (cloneConditionItem.value === false) {
    cloneConditionItem.value = '0'
}
if (cloneConditionItem.value === true) {
    cloneConditionItem.value = '1'
}
conditionData.value = { ...conditionData.value, ...cloneConditionItem }



const searchKey: Ref<string> = ref('')

const searchConditions = computed(() => {
    if (searchKey.value) {
        let list: ISearchConditions[] = []
        let reg = new RegExp(searchKey.value, 'g')
        const addedKeys = new Set<string>() // 用于跟踪已经添加的 key 值

        cacheHighSearchRulesData.forEach((category: IHighSearchRules) => {
            if (category.children) {
                list = list.concat(category.children.filter(ch => {
                    if (!ch.name || addedKeys.has(ch.key)) {
                        return false
                    } else {
                        return reg.test(ch.name)
                    }
                }).map(ch => {
                    addedKeys.add(ch.key) // 添加 key 到 Set 中
                    return {
                        ...ch,
                        operator: 'IN',
                        value: [],
                        prop: ch.key,
                        propLabel: ch.name
                    }
                }))
            }
        })
        return list
    } else {
        return cacheHighSearchRulesData.find((item: IHighSearchRules) => {
            return item.name === activeCategoryName.value
        })?.children || []
    }
})


const choseCondition = (condition: IHighSearchRuleItem) => {
    console.log('conditionData',condition)
    conditionData.value.prop = condition.key
    conditionData.value.propLabel = condition.name
    conditionData.value.dataType = condition.dataType
    conditionData.value.unit = condition.unit || ''
    if (condition.dataType === 'number') {
        conditionData.value.value = [0, 0]
        conditionData.value.operator = 'IN'
    } else if (condition.dataType === 'select') {
        conditionData.value.operator = 'IS'
        conditionData.value.value = condition.enums[0].tagValue
    } else if(condition.dataType === 'mapped'){
        conditionData.value.operator = 'IN'
        conditionData.value.value = []
    }else if(condition.dataType === 'multiSelect'){
        conditionData.value.operator = 'IN'
        conditionData.value.value = []
    }else if(condition.dataType === 'itemInput'){
        conditionData.value.operator = 'IN'
        conditionData.value.value = [] 
    }else{
        conditionData.value.value = []
    }
    console.log('conditionData',condition)
    conditionPopover.value = false
}

const getConditionEnums = () => {
    let enums: Array<{
        name: string;
        tagValue: string | number
    }> = []
    cacheHighSearchRulesData.forEach((category: IHighSearchRules) => {
        if (category.children) {
            let res = category.children.find(item => { return item.key === conditionData.value.prop })
            if (res && res.needSearch) {
                enums = store.state.app.staticConfig[res.needSearch]
            } else if (res) {
                enums = res.enums
            }
        }
    })
    return enums
}

const handleSelectChange = () => {
}

const handleCascaderChange = () => {
    let nodes = cascaderRef.value?.getCheckedNodes()
    conditionData.value.valueLabel = nodes?.map((item: { value: string, label: string, level: number }) => { return `${item.value},${item.label},${item.level}` })
}



let debounceTimer: ReturnType<typeof setTimeout> | null = null
watch(conditionData, (nVal) => {
    if(nVal.dataType === 'number'){
        if (debounceTimer) {
            clearTimeout(debounceTimer)
        }
        debounceTimer = setTimeout(() => {
            emits('updateCondition', nVal)
        }, 500)
    }else{
        emits('updateCondition', nVal)
    }
}, { deep: true })


const removeCondition = () => {
    emits('removeCondition')
}

const onClickOutside = () => {
    conditionPopover.value = false
}

onMounted(() => { })
</script>

<style lang='scss' scoped>
.high-search-rules-item-key {
    min-height: 32px;
    box-sizing: border-box;
    padding: 4px 12px;
    min-width: 220px;
    line-height: 24px;
    color: var(--el-text-color-placeholder);
    display: flex;
    box-shadow: 0 0 0 1px var(--border-color) inset;
}

.high-search-rules-item-operator {
    width: 180px;
}

.high-search-rules-item-value {
    width: 500px;

    &:deep .el-form-item {
        margin-bottom: 0
    }
}

.condition-item {
    --el-border-color: #e8e8e8;
    --el-text-color-regular: var(--el-text-color-placeholder);
}

.chose-condition-input {
    border-bottom: 1px solid var(--el-border-color);
}

.chose-condition-left {
    border-right: 1px solid var(--border-color);
    width: 110px;

    .chose-condition-category-item {
        width: 96px;
    }
}


@mixin active-chose-condition-category-item {
    background-color: var(--active-bg-);
    color: var(--main-blue-);
}

.chose-condition-category-item:hover {
    @include active-chose-condition-category-item
}

.active-chose-condition-category-item {
    @include active-chose-condition-category-item
}

.children-condition-item {
    width: 130px;
    height: 50px;

    &:hover {
        @include active-chose-condition-category-item
    }
}
</style>