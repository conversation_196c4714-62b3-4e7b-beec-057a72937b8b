<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { RISK_LIST_SEARCH_OPTIONS } from '@/js/search-options'
import { RISK_LIST_TABLE_COLUMNS } from '@/js/table-options'
import { getItem } from '@/utils/storage'
import { parseTime } from '@/utils/parse-time'
import SearchBox from '@/components/common/SearchBox.vue'
import ImportLead from '@/views/leads/components/ImportLead.vue'
import ExportLead from '@/views/leads/components/ExportLead.vue'
import AddRiskDialog from '@/views/risk-management/risk-alerts/components/AddRiskDialog.vue'
import crmService from '@/service/crmService'
import BatchUpdateContact from '@/views/leads/components/BatchUpdateContact.vue'
import RiskDialog from '@/views/risk-management/risk-alerts/components/RiskDialog.vue'
import CrmDetailDrawer from '@/components/crm/CrmDetailDrawer.vue'
import type { TableInstance } from 'element-plus'
import type { ILeadColumn, IGetCrmLeadParams, IRiskListItem, ICrmRiskRiskListParams } from '@/types/lead'


let queryParams = reactive<ICrmRiskRiskListParams>({
    page: 1,
    pageSize: 20,
})
let pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
})
const updateSearchParams = (params: IGetCrmLeadParams) => {
    queryParams = params
    clearAllSelected()
    search()
}
const search = async () => {
    tableLoading.value = true
    queryParams.page = pageInfo.page
    queryParams.pageSize = pageInfo.pageSize
    try {
        const res = await crmService.crmRiskRiskList(queryParams)
        const { errCode, data, total } = res
        if (errCode === 0) {
            tableData.value = data
            pageInfo.total = total
        } else {
            console.log('获取风险列表失败', res)
        }
        tableLoading.value = false
    } catch (error) {
        tableLoading.value = false
        console.log('error', error)
    }
}
const handleRefreshData = () => {
    clearAllSelected()
    search()
}
const riskLevel2Info = (label?: number) => {
    if (label === 100) {
        return {
            type: 'danger',
            label: '高风险',
        }
    } else if (label === 50) {
        return {
            type: 'warning',
            label: '中风险',
        }
    } else if (label === 1) {
        return {
            type: 'success',
            label: '低风险',
        }
    } else if (label === 0) {
        return {
            type: 'primary',
            label: '无风险',
        }
    } else {
        return {
            type: 'primary',
            label: '无风险',
        }
    }
}

const selectedData = ref<IRiskListItem[]>([])
const checkIds = ref<string[]>([])
const tableAllOptions = ref<ILeadColumn[]>(RISK_LIST_TABLE_COLUMNS)
const tableData = ref<IRiskListItem[]>([])
const tableLoading = ref(false)

const tableHeight = ref(500)
const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
const actionBarContentRef = ref<HTMLDivElement | null>(null)
const tableListRef = ref<TableInstance>()
// 获取table高度
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value && actionBarContentRef.value) {
        tableHeight.value = mainContentRef.value.clientHeight - searchContentRef.value.clientHeight - actionBarContentRef.value.clientHeight - 32 - 16 
    }
}
// 初始化表格列
const refreshTableCloumns = () => {
    const savedTableConfig = getItem('RISK_LIST_TABLE_COLUMNS')
    if (savedTableConfig) {
        tableAllOptions.value = savedTableConfig
    } else {
        tableAllOptions.value = JSON.parse(JSON.stringify(RISK_LIST_TABLE_COLUMNS))
    }
    tableAllOptions.value.push({
        id: 23,
        label: '操作',
        key: 'action',
        prop: 'action',
        width: '140',
        fixed: 'right',
        editable: false,
        type: 'default',
        isShow: true,
    })
}
onMounted(() => {
    getTableHeight()
    refreshTableCloumns()
    search()
})
const addVisible=ref(false)
const handleAddLead = () => {
    addVisible.value = true
}
const handleSelectionChange = (val: IRiskListItem[]) => {
    if (val.length < 2001) {
        selectedData.value = val
        checkIds.value = val.map((i) => {
            return i.id
        })
    } else {
        let newRow = val.slice(2000)
        for (let index = 0; index < newRow.length; index++) {
            tableListRef.value!.toggleRowSelection(newRow[index], false)
        }
    }
}
// 清空用户的选择
const clearAllSelected = () => {
    if (tableListRef.value) {
        tableListRef.value.clearSelection()
    }
}
const pageChange = (currentPage: number, pageSize: number) => {
    queryParams.page = currentPage
    queryParams.pageSize = pageSize
    search()
}
// 查看详情
const crmId = ref('')
const crmDetailDrawerFlag = ref(false)
const handleDetail = (id: string) => {
    crmId.value = id
    crmDetailDrawerFlag.value = true
}

const riskDialogVisible = ref(false)
const targetCompant=ref<IRiskListItem | null>(null)
const showRisk = (row: IRiskListItem) => {
    targetCompant.value = row
    // targetCompant.value.socialCreditCode = '91320191MA1ML4CL25'
    riskDialogVisible.value=true
}

</script>
<template>
    <div ref="mainContentRef" class="height-100 oa">
        <div ref="searchContentRef" class="b-margin-16 all-padding-16" style="background-color: #fff">
            <SearchBox
                :searchOptionKey="'RISK_LIST_SEARCH_OPTIONS'"
                @updateSearchParams="updateSearchParams"
            ></SearchBox>
        </div>
        <div
            ref="tableContentRef"
            class="l-padding-16 t-padding-16 r-padding-16 table-content"
            style="background-color: #fff; box-sizing: border-box"
        >
            <!-- 工具条 -->
            <div ref="actionBarContentRef" class="display-flex top-bottom-center space-between action-bar">
                <div class="display-flex top-bottom-center r-margin-16">
                    <div class="choose-content">已选{{ selectedData.length }}</div>
                    <div class="r-margin-16">
                        <el-dropdown>
                            <el-button class="no-focus-visible color-black">
                                批量操作
                                <el-icon class="el-icon--right">
                                    <CaretBottom />
                                </el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item>
                                        <BatchUpdateContact :selectedData="selectedData" :from="'riskAlert'" @clearAllSelected="clearAllSelected"></BatchUpdateContact>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                    <!-- 导出 -->
                    <div class="r-margin-16">
                        <ExportLead :from="'riskList'" :checkedIds="checkIds" :queryParams="queryParams" />
                    </div>
                    <!-- 导入 -->
                    <div class="r-margin-16">
                        <ImportLead :from="'riskList'" @refreshData="handleRefreshData" />
                    </div>
                </div>
                <div class="display-flex">
                    <el-button class="back-color-blue" type="primary" @click="handleAddLead">新增</el-button>
                </div>
            </div>

            <!-- 表格区域 -->
            <el-table
                ref="tableListRef"
                :data="tableData"
                v-loading="tableLoading"
                row-key="id"
                @selection-change="handleSelectionChange"
                :style="{ 'min-height': tableHeight + 'px' }"
            >
                <el-table-column type="selection" width="55" fixed="left" reserve-selection />
                <el-table-column
                    v-for="columns in tableAllOptions.filter((item) => item.isShow === true)"
                    :key="columns.key"
                    :prop="columns.prop"
                    :label="columns.label"
                    :width="columns.width"
                    :type="columns.type"
                    :fixed="columns.fixed"
                    :sortable="columns.sortable"
                >
                    <template #default="scope">
                        <template v-if="columns.prop === 'companyName'">
                            <div style="color: var(--main-blue-); cursor: pointer" @click="handleDetail(scope.row.id)">
                                {{ scope.row.companyName || '-'}}
                            </div>
                        </template>
                        <template v-else-if="columns.prop === 'source'">
                            {{
                                RISK_LIST_SEARCH_OPTIONS.find((item) => {
                                    return item.key === 'source'
                                })?.options?.find((i) => {
                                    return i.value === scope.row.source
                                })?.label || '-'
                            }}
                        </template>
                        <template v-else-if="columns.prop === 'riskLevel'">
                            <el-tag
                                effect="plain"
                                class="pointer"
                                :type="riskLevel2Info(scope.row.riskLevel).type"
                                v-if="scope.row.riskLevel || scope.row.riskLevel === 0"
                            >
                                {{ riskLevel2Info(scope.row.riskLevel).label }}
                            </el-tag>
                            <el-tag class="ml-2" v-else type="info">暂无风险等级</el-tag>
                        </template>
                        <template v-else-if="columns.prop === 'updateTime'">
                            {{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') || '-' }}
                        </template>
                        <template v-else-if="columns.key === 'action'">
                            <div class="display-flex">
                                <el-button type="primary" link @click="handleDetail(scope.row.id)">详情</el-button>
                                <el-button type="primary" link @click="showRisk(scope.row)">查看风险</el-button>
                            </div>
                        </template>
                        <span v-else>{{ scope.row[columns.prop] || '-' }}</span>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页器 -->
            <el-affix v-show="!tableLoading" target=".table-content" position="bottom" :offset="16">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        :page-sizes="[20, 40, 60, 100]"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>
        </div>
    </div>
    <CrmDetailDrawer
        v-model:drawer="crmDetailDrawerFlag"
        v-if="crmDetailDrawerFlag"
        :crmId="crmId"
        @refreshData="handleRefreshData"
    />
    <AddRiskDialog v-model:visible="addVisible" @refreshData="handleRefreshData"></AddRiskDialog>
    <RiskDialog v-model:visible="riskDialogVisible" :companyInfo="targetCompant"/>
</template>
<style scoped lang="scss">
@use '@/styles/element-lead.scss';
.action-bar {
    color: #303133;
    margin-bottom: 16px;
    .choose-content {
        font-size: 14px;
        color: #a6a6a6;
        margin-right: 16px;
    }
    .turn-content {
        margin-right: 16px;
    }
}
.el-dropdown-link {
    display: block;
    cursor: pointer;
    color: var(--main-blue-);
    font-size: 16px;
}
:deep(.el-dropdown-menu__item) {
    padding: 2px 16px;
}
:deep(.el-table.is-scrolling-left th.el-table-fixed-column--left) {
    background-color: #f5f7fa;
}
:deep(.el-table__header-wrapper tr th.el-table-fixed-column--left) {
    background-color: #f5f7fa;
}
:deep(.el-button) {
    font-weight: 400;
}

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}
.action-bar-fixed {
    position: fixed;
    top: 84px;
    right: 0px;
    background-color: #fff;
    z-index: 10;
    padding: 6px 16px;
    box-shadow: 0px 10px 10px #f0f0f0;
}
</style>
