<script lang="ts" setup>
// import OrgProfile from '../org/OrgProfile.vue'
import type { RootState } from '@/types/store'
import { computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore<RootState>()

const defaultOrg = computed(() => {
    const { account } = store.state.user || {}
    const { user, orgs } = account || {}
    const { defaultOrg } = user || {}

    if (!defaultOrg) return null
    if (!orgs || !Array.isArray(orgs) || orgs.length === 0) return null

    const target = orgs.find((org) => org.id === defaultOrg)

    if (!target) return null

    return target
})
const defaultTenantName = computed(() => {
    const { account } = store.state.user || {}
    const { tenant } = account || {}
    const { name } = tenant || {}
    return name || '' 
})

// const props = defineProps<{
//     showSwitcher: () => void
// }>()
const getShortName = (str: string) => {
    const pattern = /([\u4e00-\u9fa5]{2,}省)/
    const res = str.replace(pattern, '')
    const provinces = [
        '北京', '天津', '河北', '山西', '辽宁', '吉林', '黑龙江',
        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东',
        '河南', '湖北', '湖南', '广东', '海南', '四川', '贵州',
        '云南', '陕西', '甘肃', '青海', '台湾', '内蒙古', '广西',
        '西藏', '宁夏', '新疆', '香港', '澳门', '重庆'
    ]

    // 按长度降序排序，确保优先匹配长名称
    provinces.sort((a, b) => b.length - a.length)

    // 构建正则表达式
    const regex = new RegExp(
        `^(${provinces.map(p =>
            // 处理特殊字符并添加边界匹配
            p.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
        ).join('|')})`
    )

    // 去除匹配到的省级行政区划
    const cleanedStr = res.replace(regex, '')

    const patterncity = /([\u4e00-\u9fa5]{2,}市)/
    const cleanedStringCity = cleanedStr.replace(patterncity, '')

    // 返回前四个字符
    return cleanedStringCity.slice(0, 4).replace(/[（）()]/g, '')
}
</script>

<template>
    <div class="split-line" v-if="defaultOrg"></div>
    <div class="flex flex-column all-padding-16 gap-12" v-if="defaultOrg">
        <div class="display-flex top-bottom-center">
            <div class="w-30 h-30 border-radius-30 r-margin-10 display-flex center" style="background-color: #CCDDFF;">
                <Icon icon="icon-a-huaban43" color="#1966ff" size="25"/>
            </div>
            <div class="flex-1">
                <div class="font-14 color-three-grey">所属组织</div>
                <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="`${defaultTenantName}＞${defaultOrg.name}`"
                    placement="top"
                >
                    <div class="width-100 color-black font-16 lh-16 text-ellipsis">{{ defaultTenantName }}＞{{ getShortName(defaultOrg.name) }}</div>
                </el-tooltip>
                <!-- <OrgProfile
                    :name="defaultOrg?.name || ''"
                    :id="defaultOrg?.id || ''"
                    :tags="[]"
                    :show-text="true"
                    :click="props.showSwitcher"
                /> -->
            </div>
        </div>
        
    </div>
</template>

<style lang="scss" scoped>
.text-ellipsis{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
