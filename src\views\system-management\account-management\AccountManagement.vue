<template>
    <div ref="mainContentRef" class="height-100 oa">
        <!-- 搜索栏 -->
        <div ref="searchContentRef" class="b-margin-16 all-padding-16" style="background-color: #fff">
            <SearchBox :searchOptionKey="'ACCOUNT_MANAGEMENT_SEARCH_OPTIONS'" :customConfig="customSearchOptionConifg" @updateSearchParams="updateSearchParams"></SearchBox>
        </div>
        <!-- 表格栏 -->
        <div class="all-padding-16" style="background-color: #fff; box-sizing: border-box">
            <!-- 工具条 -->
            <div ref="actionBarContentRef" class="display-flex top-bottom-center action-bar">
                <el-button type="primary" @click="handleAddAccount">新增</el-button>
            </div>
            <!-- 表格 -->
            <el-table
                ref="tableListRef"
                :height="tableHeight + 'px'"
                :data="tableData"
                v-loading="tableLoading"
                row-key="id"
                header-row-class-name="tableHeader"
            >
                <el-table-column label="登录账号" prop="username">
                    <template #default="scope">
                        <span>{{ scope.row.username || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="联系人姓名" prop="nickname">
                    <template #default="scope">
                        <span>{{ scope.row.nickname || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="手机号码" prop="mobile">
                    <template #default="scope">
                        <span>{{ scope.row.mobile|| '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="状态" prop="status" >
                    <template #default="scope">
                        <el-dropdown>
                            <el-tag class="pointer" :type="scope.row.status == 1 ? 'info' : 'success'">
                                <div class="flex flex-row gap-4 top-bottom-center">
                                    {{ scope.row.status === 1 ? '停用' : '启用' }} <el-icon><ArrowDown /></el-icon>
                                </div>
                            </el-tag>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click="changeStatus(0, scope.row)" v-if="scope.row.status === 1">
                                        启用
                                    </el-dropdown-item>
                                    <el-dropdown-item @click="changeStatus(1, scope.row)" v-if="scope.row.status === 0">
                                        停用
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </el-table-column>
                <el-table-column label="用户角色" prop="roleName">
                    <template #default="scope">
                        <span> {{ roleList?.find(item => item.roleId === scope.row.role[0])?.roleName || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="createTime">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="创建人" prop="createUser">
                    <template #default="scope">
                        <span>{{ scope.row.createUser || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                    <template #default="scope">
                        <el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页器 -->
            <el-affix position="bottom" :offset="0">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        :page-sizes="[20, 40, 60, 100]"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>
        </div>
    </div>
    <UserUpdateDialog
        :visible="userUpdateDialogVisible"
        :onClose="onUpdateDialogVisible"
        :type="updateType"
        :user="currentUser"
        :fromAccountManagement="true"
    />
</template>
<script lang="ts" setup>
import { ref, onMounted, reactive, onBeforeMount } from 'vue'
import SearchBox from '@/components/common/SearchBox.vue'
import UserUpdateDialog from '@/views/system-management/internal-organization/components/organization/components/UserUpdateDialog.vue'
import userService from '@/service/userService'
import systemService from '@/service/systemService'
import type { IUserplatUserPageItem, IUserplatUserPageRequest, IPageUserItem } from '@/types/user'
import type { IRoleItem } from '@/types/role'
import { ElMessage } from 'element-plus'
import { parseTime } from '@/utils/parse-time'

const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
const actionBarContentRef = ref<HTMLDivElement | null>(null)
const tableHeight = ref(500)
const tableLoading = ref(false)
const tableData = ref<IUserplatUserPageItem[]>()
const userUpdateDialogVisible = ref(false)
const updateType = ref('add')
let pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
})

let queryParams = reactive<IUserplatUserPageRequest>({
    page: 1,
    pageSize: 20,
})
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value && actionBarContentRef.value) {
        tableHeight.value =
            mainContentRef.value.clientHeight -
            searchContentRef.value.clientHeight -
            actionBarContentRef.value.clientHeight -
            32 -
            16 -
            16 -
            16 -
            16 -
            16
    }
}
const search = async () => {
    tableLoading.value = true
    try {
        let userplatUserPageRes = await userService.userplatUserPage(queryParams)
        const { data } = userplatUserPageRes
        tableData.value = data
        tableLoading.value = false
    } catch (error) {
        console.log('查询用户列表失败', error)
        tableLoading.value = false
    }
}
const updateSearchParams = (params: IUserplatUserPageRequest) => {
    console.log('updatSearchParams', params)
    queryParams = params
    queryParams.page = 1
    queryParams.pageSize =20
    search()
}
const roleList=ref<IRoleItem[]>()
const getRoleList = async () => {
    try {
        let roleListRes = await systemService.roleListByName()
        const { data } = roleListRes
        roleList.value = data
    } catch (error) {
        console.log('获取角色列表失败',error)
        ElMessage({
            type: 'error',
            message: '获取角色列表失败',
        })
    }
}
type CustomConfig = {
    [key: string]: Array<{
        label: string
        value: string
    }>
}
const customSearchOptionConifg = ref<CustomConfig>({})
const getSearchOptions = async () => {
    await getRoleList()
    const transRoleList = roleList.value.map((item) => ({ value: item.roleId, label: item.roleName }))
    customSearchOptionConifg.value = {
        roleId:  transRoleList
    }
}
onBeforeMount(() => {
    getSearchOptions()
})
onMounted(() => {
    getTableHeight()
    search()
})
const pageChange = (currentPage: number, pageSize: number) => {
    queryParams.page = currentPage
    queryParams.pageSize = pageSize
    search()
}
const changeStatus = (status: number, user: IUserplatUserPageItem) => {
    systemService
        .userEdit({
            id: user.id,
            nickname: user.nickname,
            role: user.role,
            status: status.toString(),
            username: user.username,
        })
        .then((res) => {
            const { errCode, errMsg } = res
            if (errCode !== 0) {
                ElMessage.error(errMsg || '操作失败')
            } else {
                search()
            }
        })
        .catch((err) => {
            ElMessage.error(err || '操作失败，请稍后再试')
        })
}

const handleAddAccount = () => {
    userUpdateDialogVisible.value = true
    
}
const currentUser = ref<IPageUserItem | null>(null)
const handleEdit = (row: IPageUserItem ) => {
    updateType.value = 'edit'
    userUpdateDialogVisible.value = true
    currentUser.value = row
}
const onUpdateDialogVisible = (refresh?: boolean) => {
    userUpdateDialogVisible.value = false
    currentUser.value = null
    if (refresh) {
        search()
    }
}
</script>
<style scoped lang="scss">
@use '@/styles/element-lead.scss';
.action-bar {
    color: #303133;
    margin-bottom: 16px;
    flex-direction: row-reverse;
    .choose-content {
        font-size: 14px;
        color: #a6a6a6;
        margin-right: 16px;
    }
    .turn-content {
        margin-right: 16px;
    }
}

.el-dropdown-link {
    display: block;
    cursor: pointer;
    color: var(--main-blue-);
    font-size: 16px;
}
:deep(.el-dropdown-menu__item) {
    padding: 2px 16px;
}
:deep(.el-table.is-scrolling-left th.el-table-fixed-column--left) {
    background-color: #f5f7fa;
}
:deep(.el-table__header-wrapper tr th.el-table-fixed-column--left) {
    background-color: #f5f7fa;
}
:deep(.el-button) {
    font-weight: 400;
}

.tableHeader {
    font-size: 24px;
}

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}
</style>
